from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from firebase_admin import auth as firebase_auth
from firebase.config import get_db
from auth.deps import verify_firebase_token
from pydantic import BaseModel
from google.cloud import firestore
import logging
import resend
import os
from typing import Optional

logger = logging.getLogger(__name__)

# Initialize Resend with API key from environment variable
resend.api_key = os.environ.get("RESEND_API_KEY")

class UpdateProfilePayload(BaseModel):
    name: str | None = None
    agentName: str | None = None  # Added for chat agent name
    keywords: list[str] | None = None
    logo_url: str | None = None
    industry: str | None = None
    company_name: str | None = None
    payout_account: str | None = None
    domain: str | None = None

class UpdateOnboardingPayload(BaseModel):
    onboardingStatus: str | None = None  # 'pending' | 'completed'
    seenPioneerWelcome: bool | None = None
    xp: int | None = None
    lifetime_xp: int | None = None

router = APIRouter()
db = get_db()

@router.get("/auth/me")
async def get_current_user(decoded_token = Depends(verify_firebase_token)):
    try:
        uid = decoded_token["uid"]
        role = decoded_token.get("role")

        if not role:
            raise HTTPException(status_code=403, detail="No role assigned to user")

        # Initialize data with basic user info from token
        data = {
            "uid": uid,
            "email": decoded_token.get("email", ""),
            "name": decoded_token.get("name", ""),
        }

        # Try to get user document from Firestore
        try:
            base_profile = db.collection("users").document(uid).get()
            if base_profile.exists:
                data.update(base_profile.to_dict())
            else:
                # Create a basic user document if it doesn't exist
                default_user_data = {
                    "uid": uid,
                    "email": decoded_token.get("email", ""),
                    "name": decoded_token.get("name", ""),
                    "created_at": firestore.SERVER_TIMESTAMP,
                }

                if role == "user":
                    default_user_data.update({
                        "onboardingStatus": "pending",
                        "seenPioneerWelcome": False,
                        "xp": 0,
                        "lifetime_xp": 0,
                    })

                db.collection("users").document(uid).set(default_user_data)
                data.update(default_user_data)
        except Exception as e:
            # Log the error but continue with basic data
            logger.error(f"Error accessing user document: {str(e)}")

        # Try to load role-specific data
        try:
            if role == "agent":
                role_doc = db.collection("agents").document(uid).get()
                if role_doc.exists:
                    data.update(role_doc.to_dict())
            elif role == "brand":
                role_doc = db.collection("brands").document(uid).get()
                if role_doc.exists:
                    data.update(role_doc.to_dict())
        except Exception as e:
            # Log the error but continue with basic data
            logger.error(f"Error accessing role-specific document: {str(e)}")

        # Ensure onboarding fields are present for users
        if role == "user":
            # Set default values if fields don't exist
            if "onboardingStatus" not in data:
                data["onboardingStatus"] = "pending"
            if "seenPioneerWelcome" not in data:
                data["seenPioneerWelcome"] = False
            if "xp" not in data:
                data["xp"] = 0
            if "lifetime_xp" not in data:
                data["lifetime_xp"] = data.get("xp", 0)

            # Check if user is eligible for Pioneer program
            try:
                counter_ref = db.collection("counters").document("pioneer_users")
                counter_doc = counter_ref.get()

                if counter_doc.exists:
                    current_count = counter_doc.to_dict().get("count", 0)
                    data["isPioneerEligible"] = current_count < 500
                else:
                    data["isPioneerEligible"] = True  # First users are always eligible
            except Exception as e:
                logger.error(f"Error checking pioneer eligibility: {str(e)}")
                data["isPioneerEligible"] = True  # Default to eligible on error

        return data
    except Exception as e:
        logger.error(f"Error in get_current_user: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get user data: {str(e)}")

@router.patch("/auth/update-profile")
async def update_profile(payload: UpdateProfilePayload, decoded_token = Depends(verify_firebase_token)):
    uid = decoded_token["uid"]
    role = decoded_token.get("role")

    if not role:
        raise HTTPException(status_code=403, detail="No role assigned to user")

    update_data = {k: v for k, v in payload.model_dump().items() if v is not None}

    if not update_data:
        raise HTTPException(status_code=400, detail="No fields provided for update")

    # Update role-specific collection
    if role == "agent":
        db.collection("agents").document(uid).update(update_data)
    elif role == "brand":
        db.collection("brands").document(uid).update(update_data)
    else:
        db.collection("users").document(uid).update(update_data)

    return {"status": "success", "updated": update_data}

@router.patch("/auth/update-onboarding")
async def update_onboarding(payload: UpdateOnboardingPayload, decoded_token = Depends(verify_firebase_token)):
    """Update user onboarding status, pioneer welcome flag, and XP"""
    uid = decoded_token["uid"]
    role = decoded_token.get("role")

    if not role:
        raise HTTPException(status_code=403, detail="No role assigned to user")

    # Only users can update onboarding status
    if role != "user":
        raise HTTPException(status_code=403, detail="Only users can update onboarding status")

    update_data = {k: v for k, v in payload.model_dump().items() if v is not None}

    if not update_data:
        raise HTTPException(status_code=400, detail="No fields provided for update")

    # Update user document
    db.collection("users").document(uid).update(update_data)

    return {"status": "success", "updated": update_data}

@router.post("/auth/send-verification-email")
async def send_verification_email(
    background_tasks: BackgroundTasks,
    decoded_token = Depends(verify_firebase_token)
):
    """Send email verification email to the user"""
    try:
        uid = decoded_token["uid"]
        email = decoded_token.get("email")

        if not email:
            raise HTTPException(status_code=400, detail="No email found in token")

        if not resend.api_key:
            raise HTTPException(status_code=500, detail="Email service not configured")

        # Generate verification link (you can customize this URL)
        verification_url = f"{os.environ.get('FRONTEND_URL', 'https://dashboard.useadmesh.com')}/verify-email?uid={uid}"

        # Email content
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Verify Your Email - AdMesh</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="text-align: center; margin-bottom: 30px;">
                <h1 style="color: #2563eb;">AdMesh</h1>
            </div>

            <h2 style="color: #1f2937;">Verify Your Email Address</h2>

            <p>Hello,</p>

            <p>Thank you for signing up with AdMesh! To complete your registration and ensure you receive important updates, please verify your email address by clicking the button below:</p>

            <div style="text-align: center; margin: 30px 0;">
                <a href="{verification_url}"
                   style="background-color: #2563eb; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
                    Verify Email Address
                </a>
            </div>

            <p>If the button doesn't work, you can also copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #2563eb;">{verification_url}</p>

            <p>If you didn't create an account with AdMesh, you can safely ignore this email.</p>

            <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">

            <p style="font-size: 14px; color: #6b7280;">
                Best regards,<br>
                The AdMesh Team<br>
                <a href="https://useadmesh.com" style="color: #2563eb;">useadmesh.com</a>
            </p>
        </body>
        </html>
        """

        # Send email using Resend
        params = {
            "from": "AdMesh <<EMAIL>>",
            "to": [email],
            "subject": "Verify Your Email Address - AdMesh",
            "html": html_content,
        }

        email_result = resend.Emails.send(params)

        logger.info(f"Verification email sent successfully to {email}")
        return {
            "status": "success",
            "message": "Verification email sent successfully",
            "email_id": email_result.get("id")
        }

    except Exception as e:
        logger.error(f"Error sending verification email: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to send verification email: {str(e)}")

@router.post("/auth/verify-email")
async def verify_email_endpoint(
    uid: str,
    decoded_token = Depends(verify_firebase_token)
):
    """Mark user's email as verified"""
    try:
        # Verify that the user is verifying their own email or is an admin
        token_uid = decoded_token["uid"]
        if token_uid != uid and not decoded_token.get("admin", False):
            raise HTTPException(status_code=403, detail="Can only verify your own email")

        # Get current custom claims
        try:
            user_record = firebase_auth.get_user(uid)
            current_claims = dict(user_record.custom_claims or {})
        except Exception as e:
            raise HTTPException(status_code=404, detail=f"User not found: {str(e)}")

        # Update Firebase custom claims
        updated_claims = {**current_claims, "email_verified": True}
        firebase_auth.set_custom_user_claims(uid, updated_claims)

        # Update user document in Firestore
        user_ref = db.collection("users").document(uid)
        if user_ref.get().exists:
            user_ref.update({
                "email_verified": True,
                "updated_at": firestore.SERVER_TIMESTAMP
            })

        # Update agent document if user is an agent
        if decoded_token.get("role") == "agent":
            agent_ref = db.collection("agents").document(uid)
            if agent_ref.get().exists:
                agent_ref.update({
                    "email_verified": True,
                    "updated_at": firestore.SERVER_TIMESTAMP
                })

        logger.info(f"Email verified successfully for user {uid}")
        return {
            "status": "success",
            "message": "Email verified successfully"
        }

    except Exception as e:
        logger.error(f"Error verifying email: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to verify email: {str(e)}")
