"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { motion } from "framer-motion";
import {
  PlayCircle,
  Package,
  FileText,
  Sparkles,
  TrendingUp
} from "lucide-react";

// Import existing components
import InteractiveAgentDemo from "@/components/InteractiveAgentDemo";
import AgentEarningsCalculator from "@/components/AgentEarningsCalculator";

export default function DemoPage() {
  return (
    <div className="p-4 sm:p-6 space-y-6 sm:space-y-8 max-w-7xl mx-auto">
      {/* Header */}
      <div className="text-center space-y-3 sm:space-y-4">
        <div className="flex flex-col sm:flex-row items-center justify-center gap-2">
          <div className="flex items-center gap-2">
            <PlayCircle className="h-6 w-6 sm:h-8 sm:w-8 text-primary" />
            <h1 className="text-2xl sm:text-3xl font-bold">Try AdMesh Demo</h1>
          </div>
          <Badge variant="secondary" className="sm:ml-2">
            <Sparkles className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
            Interactive
          </Badge>
        </div>
        <p className="text-base sm:text-lg text-muted-foreground max-w-3xl mx-auto px-4 sm:px-0">
          Experience real AdMesh UI components with live data.
          <strong> Developers have complete freedom</strong> - use our pre-built components or create your own custom designs using our data API.
        </p>
      </div>

      {/* Interactive Demo Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Card>
          <CardHeader className="pb-4 sm:pb-6">
            <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
              <Package className="h-4 w-4 sm:h-5 sm:w-5" />
              Live Ad Unit Components
            </CardTitle>
            <p className="text-xs sm:text-sm text-muted-foreground">
              Try different ad unit types with real AdMesh SDK components. All components are interactive and use live demo data.
            </p>
          </CardHeader>
          <CardContent className="px-4 sm:px-6">
            <InteractiveAgentDemo />
          </CardContent>
        </Card>
      </motion.div>

      {/* Earnings Calculator Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <Card>
          <CardHeader className="pb-4 sm:pb-6">
            <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
              <TrendingUp className="h-4 w-4 sm:h-5 sm:w-5" />
              Revenue Calculator
            </CardTitle>
            <p className="text-xs sm:text-sm text-muted-foreground">
              Calculate your potential earnings based on your platform&apos;s traffic and user engagement.
            </p>
          </CardHeader>
          <CardContent className="px-4 sm:px-6">
            <AgentEarningsCalculator hideCTA={true} />
          </CardContent>
        </Card>
      </motion.div>

      {/* Developer Freedom Notice */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 border-primary/20">
          <CardContent className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row items-start gap-3 sm:gap-4">
              <div className="p-2 bg-primary/10 rounded-lg flex-shrink-0">
                <FileText className="h-5 w-5 sm:h-6 sm:w-6 text-primary" />
              </div>
              <div className="space-y-2 w-full">
                <h3 className="text-base sm:text-lg font-semibold">Complete Developer Freedom</h3>
                <p className="text-sm sm:text-base text-muted-foreground">
                  You&apos;re not limited to our UI components. AdMesh provides:
                </p>
                <ul className="space-y-1 text-xs sm:text-sm text-muted-foreground">
                  <li>• <strong>Pre-built Components:</strong> Ready-to-use React components for quick integration</li>
                  <li>• <strong>Data API:</strong> Raw recommendation data to build your own custom UI</li>
                  <li>• <strong>Flexible Styling:</strong> Customize our components or create entirely new designs</li>
                  <li>• <strong>Multiple Formats:</strong> Choose from inline, cards, citations, summaries, and more</li>
                </ul>
                <div className="pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full sm:w-auto"
                    onClick={() => window.open("https://docs.useadmesh.com/", "_blank")}
                  >
                    View API Documentation
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      
    </div>
  );
}