"use client";

import { useEffect, useState, useCallback } from "react";
import { useAuth } from "@/hooks/use-auth";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Calendar, TrendingUp, AlertCircle, RefreshCw, DollarSign } from "lucide-react";
import { format } from "date-fns";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";

interface Earning {
  id: string;
  amount: number;
  agent_earning: number;
  user_earning: number;
  protocol_fee: number;
  currency: string;
  timestamp: string;
  brand_id: string;
  offer_id: string;
  event_type: string;
  intent_type: string;
  brand_name?: string;
  offer_title?: string;
  reward_split?: {
    agent: number;
    user: number;
    admesh: number;
  };
  payout?: {
    amount: number;
    currency: string;
    model: string;
  };
  user_id?: string;
  is_test?: boolean;
}

export default function EarningsPage() {
  const { user } = useAuth();
  const [earnings, setEarnings] = useState<Earning[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState("30d");
  const [activeTab, setActiveTab] = useState("all");
  const [stats, setStats] = useState({
    totalEarnings: 0,
    thisMonth: 0,
    lastMonth: 0,
    avgPerConversion: 0,
    pendingPayout: 0,
  });

  const fetchEarnings = useCallback(async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      const token = await user.getIdToken();
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/agents/earnings/${user.uid}?time_range=${timeRange}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        if (response.status === 403) {
          throw new Error("You don't have permission to access earnings data");
        } else if (response.status === 404) {
          // 404 means no earnings found - this is not an error, just return empty list
          setEarnings([]);
          setStats({
            totalEarnings: 0,
            thisMonth: 0,
            lastMonth: 0,
            avgPerConversion: 0,
            pendingPayout: 0,
          });
          setLoading(false);
          return;
        } else if (response.status >= 500) {
          throw new Error("Server error. Please try again later");
        } else {
          throw new Error(`Failed to fetch earnings (${response.status})`);
        }
      }

      const data = await response.json();
      setEarnings(data.earnings || []);

        // Calculate stats using agent_earning (agent's share only)
        const totalEarnings = data.earnings?.reduce(
          (sum: number, e: Earning) => sum + (e.agent_earning || e.amount),
          0
        ) || 0;

        // Filter production earnings only (exclude test earnings)
        const productionEarnings = data.earnings?.filter((e: Earning) => !e.is_test) || [];
        const productionTotal = productionEarnings.reduce(
          (sum: number, e: Earning) => sum + (e.agent_earning || e.amount),
          0
        );

        // This is a placeholder - in a real app, you'd calculate these values properly
        const thisMonth = totalEarnings * 0.7; // Just for demo
        const lastMonth = totalEarnings * 0.3; // Just for demo
        const avgPerConversion = data.earnings?.length > 0
          ? totalEarnings / data.earnings.length
          : 0;

        setStats({
          totalEarnings,
          thisMonth,
          lastMonth,
          avgPerConversion,
          pendingPayout: productionTotal * 0.8, // 80% of production earnings pending
        });


      } catch (error) {
        console.error("Error fetching earnings:", error);
        const errorMessage = error instanceof Error ? error.message : "Failed to fetch earnings";
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
  }, [user, timeRange]);

  const handleRefresh = useCallback(async () => {
    await fetchEarnings();
    toast.success("Earnings data refreshed successfully");
  }, [fetchEarnings]);

  useEffect(() => {
    fetchEarnings();
  }, [fetchEarnings]);

  const filteredEarnings = earnings.filter((earning) => {
    if (activeTab === "all") return true;
    return earning.intent_type === activeTab;
  });

  // Format currency
  const formatCurrency = (amount: number, currency: string = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency,
      minimumFractionDigits: 2,
    }).format(amount / 100); // Assuming amount is stored in cents
  };

  const renderSkeleton = () => (
    <>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        {[...Array(3)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="pb-2">
              <Skeleton className="h-4 w-24" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-24" />
              <Skeleton className="h-4 w-32 mt-2" />
            </CardContent>
          </Card>
        ))}
      </div>
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {[...Array(5)].map((_, i) => (
              <Skeleton key={i} className="h-12 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    </>
  );

  return (
    <div className="p-4 sm:p-6 space-y-4 sm:space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Earnings</h1>
          <p className="text-sm sm:text-base text-muted-foreground">Track your revenue and payouts</p>
        </div>
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={loading}
            className="gap-2 w-full sm:w-auto"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-full sm:w-[180px]">
              <Calendar className="mr-2 h-4 w-4" />
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="all">All time</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

    
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {loading ? (
        renderSkeleton()
      ) : (
        <>
        

          {/* Stats Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Total Earnings
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(stats.totalEarnings)}
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Lifetime earnings
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  This Month
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(stats.thisMonth)}
                </div>
                <div className="flex items-center text-xs text-green-600 mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  <span>
                    {stats.lastMonth > 0
                      ? `+${(
                          ((stats.thisMonth - stats.lastMonth) /
                            stats.lastMonth) *
                          100
                        ).toFixed(1)}%`
                      : "N/A"}
                  </span>
                  <span className="text-muted-foreground ml-1">vs last month</span>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Pending Payout
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(stats.pendingPayout)}
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Available on next payout date
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Earnings Table */}
          <Card>
            <CardHeader>
              <CardTitle>Earnings History</CardTitle>
              <CardDescription>
                A detailed breakdown of your earnings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
                <TabsList>
                  <TabsTrigger value="all">All</TabsTrigger>
                  <TabsTrigger value="generic">Generic</TabsTrigger>
                  <TabsTrigger value="shopping">Shopping</TabsTrigger>
                  <TabsTrigger value="research">Research</TabsTrigger>
                </TabsList>
              </Tabs>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Brand</TableHead>
                    <TableHead>Offer</TableHead>
                    <TableHead>Total Payout</TableHead>
                    <TableHead>Your Share</TableHead>
                    <TableHead>User Share</TableHead>
                    <TableHead>Revenue Split</TableHead>
                    <TableHead>Type</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredEarnings.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-6">
                        No earnings found for the selected criteria.
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredEarnings.map((earning) => (
                      <TableRow key={earning.id}>
                        <TableCell>
                          <div className="flex flex-col">
                            <span>{format(new Date(earning.timestamp), "MMM d, yyyy")}</span>
                            <span className="text-xs text-muted-foreground">
                              {format(new Date(earning.timestamp), "HH:mm")}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span className="font-medium">{earning.brand_name || earning.brand_id}</span>
                            <span className="text-xs text-muted-foreground capitalize">
                              {earning.intent_type} • {earning.event_type}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>{earning.offer_title || earning.offer_id}</TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span className="font-medium">
                              {formatCurrency(earning.amount, earning.currency)}
                            </span>
                            {earning.payout && (
                              <span className="text-xs text-muted-foreground">
                                {earning.payout.model}
                              </span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className="font-medium text-green-600">
                            {formatCurrency(earning.agent_earning || earning.amount, earning.currency)}
                          </span>
                        </TableCell>
                        <TableCell>
                          <span className="text-blue-600">
                            {earning.user_earning ? formatCurrency(earning.user_earning, earning.currency) : '—'}
                          </span>
                        </TableCell>
                        <TableCell>
                          {earning.reward_split ? (
                            <div className="text-xs">
                              <div className="flex gap-1">
                                <span className="text-green-600">{earning.reward_split.agent}%</span>
                                <span className="text-blue-600">{earning.reward_split.user}%</span>
                                <span className="text-gray-600">{earning.reward_split.admesh}%</span>
                              </div>
                              <div className="text-muted-foreground">A/U/M</div>
                            </div>
                          ) : (
                            '—'
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col gap-1">
                            {earning.is_test && (
                              <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                                Test
                              </span>
                            )}
                            {earning.user_id ? (
                              <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                                With User
                              </span>
                            ) : (
                              <span className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
                                No User
                              </span>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}
