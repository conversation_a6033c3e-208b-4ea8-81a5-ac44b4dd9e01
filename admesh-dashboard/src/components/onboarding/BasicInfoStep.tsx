"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Bo<PERSON>, ArrowRight, Loader2, Mail, BadgeCheck, RefreshCw } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { BasicInfoStepProps, AGENT_TYPES, USER_COUNT_OPTIONS } from "@/types/onboarding";
import { useAuth } from "@/hooks/use-auth";
import { toast } from "sonner";

/**
 * BasicInfoStep component for collecting company and product information
 */
const BasicInfoStep = ({
  agentName,
  setAgentName,
  agentType,
  setAgentType,
  otherAgentType,
  setOtherAgentType,
  agentUrl,
  setAgentUrl,
  userCount,
  setUserCount,
  onNext,
  isLoading
}: BasicInfoStepProps) => {
  const { user } = useAuth();
  const [emailVerified, setEmailVerified] = useState(false);
  const [sendingVerification, setSendingVerification] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  // Check email verification status on component mount
  useEffect(() => {
    const checkEmailVerification = async () => {
      if (!user) return;

      try {
        const token = await user.getIdToken();
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/auth/me`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.ok) {
          const data = await response.json();
          setEmailVerified(data.email_verified || false);
        }
      } catch (error) {
        console.error("Error checking email verification:", error);
      }
    };

    checkEmailVerification();
  }, [user]);

  // Email verification functions
  const sendVerificationEmail = async () => {
    if (!user) return;

    setSendingVerification(true);
    try {
      const token = await user.getIdToken();
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/auth/send-verification-email`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to send verification email");
      }

      toast.success("Verification email sent! Please check your inbox and spam folder.");
    } catch (error) {
      console.error("Error sending verification email:", error);
      toast.error("Failed to send verification email. Please try again.");
    } finally {
      setSendingVerification(false);
    }
  };

  const refreshEmailStatus = async () => {
    if (!user) return;

    setRefreshing(true);
    try {
      const token = await user.getIdToken();
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/auth/me`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        setEmailVerified(data.email_verified || false);

        if (data.email_verified) {
          toast.success("Email verified successfully!");
        } else {
          toast.info("Email not yet verified. Please check your inbox.");
        }
      }
    } catch (error) {
      console.error("Error refreshing email status:", error);
      toast.error("Failed to refresh email status");
    } finally {
      setRefreshing(false);
    }
  };
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="space-y-6"
    >
      <div className="text-center space-y-2">
        <div className="mx-auto bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mb-4">
          <Bot className="w-8 h-8 text-primary" />
        </div>
        <h2 className="text-2xl font-bold">Company & Product Information</h2>
        <p className="text-muted-foreground">
          Tell us about your business and product
        </p>
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="agentName">Company Name</Label>
          <Input
            id="agentName"
            value={agentName}
            onChange={(e) => setAgentName(e.target.value)}
            placeholder="e.g. Acme Inc., TechSolutions, AI Innovations"
            className="h-12"
            required
          />
        </div>

        {/* Email Verification Section */}
        <div className="space-y-2">
          <Label htmlFor="email">Email Address</Label>
          <div className="flex items-center gap-2">
            <Input
              id="email"
              name="email"
              value={user?.email || ""}
              disabled
              className="flex-1 h-12"
            />
            <div className="flex items-center gap-2">
              {emailVerified ? (
                <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                  <BadgeCheck className="w-3 h-3 mr-1" />
                  Verified
                </Badge>
              ) : (
                <>
                  <Badge variant="destructive" className="bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400">
                    <Mail className="w-3 h-3 mr-1" />
                    Unverified
                  </Badge>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={sendVerificationEmail}
                    disabled={sendingVerification}
                    className="whitespace-nowrap"
                  >
                    {sendingVerification ? (
                      <>
                        <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                        Sending...
                      </>
                    ) : (
                      <>
                        <Mail className="w-3 h-3 mr-1" />
                        Verify
                      </>
                    )}
                  </Button>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={refreshEmailStatus}
                    disabled={refreshing}
                    className="whitespace-nowrap"
                  >
                    {refreshing ? (
                      <RefreshCw className="w-3 h-3 animate-spin" />
                    ) : (
                      <RefreshCw className="w-3 h-3" />
                    )}
                  </Button>
                </>
              )}
            </div>
          </div>
          {!emailVerified && (
            <p className="text-xs text-muted-foreground">
              Please verify your email address to ensure you receive important notifications and updates.
            </p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="agentType">Product Type</Label>
          <Select value={agentType} onValueChange={setAgentType}>
            <SelectTrigger id="agentType" className="h-12">
              <SelectValue placeholder="Select product type" />
            </SelectTrigger>
            <SelectContent>
              {AGENT_TYPES.map((type) => (
                <SelectItem key={type} value={type}>
                  {type}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {agentType === "Other" && (
          <div className="space-y-2">
            <Label htmlFor="otherAgentType">Please specify your product type</Label>
            <Input
              id="otherAgentType"
              value={otherAgentType}
              onChange={(e) => setOtherAgentType(e.target.value)}
              placeholder="e.g. Mobile App, Desktop App, Voice Assistant"
              className="h-12"
              required
            />
          </div>
        )}

        <div className="space-y-2">
          <Label htmlFor="userCount">How many users?</Label>
          <Select value={userCount} onValueChange={setUserCount}>
            <SelectTrigger id="userCount" className="h-12">
              <SelectValue placeholder="Select user count range" />
            </SelectTrigger>
            <SelectContent>
              {USER_COUNT_OPTIONS.map((option) => (
                <SelectItem key={option} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <p className="text-xs text-muted-foreground">
            This helps us understand your platform scale and optimize recommendations.
          </p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="agentUrl">Website URL</Label>
          <Input
            id="agentUrl"
            value={agentUrl}
            onChange={(e) => setAgentUrl(e.target.value)}
            placeholder="https://yourtool.com"
            className="h-12"
            required
          />
          <p className="text-xs text-muted-foreground">
            This helps us understand where traffic is coming from and builds your trust score.
          </p>
        </div>
      </div>

      <Button
        onClick={onNext}
        disabled={isLoading}
        className="w-full h-12"
      >
        {isLoading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Saving...
          </>
        ) : (
          <>
            Continue <ArrowRight className="ml-2 h-4 w-4" />
          </>
        )}
      </Button>
    </motion.div>
  );
};

export default BasicInfoStep;
